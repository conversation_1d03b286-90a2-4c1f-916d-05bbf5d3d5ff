@echo off
echo Testing OpenTM after fixes...
echo.

cd /d "%~dp0\build\Release"

echo Current directory: %CD%
echo.

echo DLL files present:
dir *.dll /b
echo.

echo Testing openTM.exe...
echo.

REM Try to run the application
openTM.exe 2>&1
echo.
echo Exit code: %ERRORLEVEL%

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Application ran without errors!
) else (
    echo FAILED: Application encountered an error.
    echo.
    echo If you still get "Entry Point Not Found":
    echo 1. Install Visual C++ Redistributable 2022 x64
    echo 2. Check CUDA installation
    echo 3. Restart your computer
)

pause
