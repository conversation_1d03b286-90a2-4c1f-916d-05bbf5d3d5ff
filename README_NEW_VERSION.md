# OpenTM 增强版本 - 基于目标张量的文件命名

## 新功能概述

本版本实现了基于目标热传导张量的智能文件命名系统，每个优化结果会自动生成两种格式的输出文件：
1. **CSV格式** - 用于数据分析和后处理
2. **VDB格式** - 用于3D可视化和渲染

## 文件命名规则

文件名基于目标热传导张量的6个分量自动生成：
```
H_{xx}_{yy}_{zz}_{xy}_{yz}_{xz}.{extension}
```

### 张量分量说明
- `xx` - H(0,0) 热传导张量的xx分量
- `yy` - H(1,1) 热传导张量的yy分量  
- `zz` - H(2,2) 热传导张量的zz分量
- `xy` - H(0,1) 热传导张量的xy分量
- `yz` - H(2,1) 热传导张量的yz分量
- `xz` - H(0,2) 热传导张量的xz分量

### 示例文件名
对于目标张量 `[0.300, 0.200, 0.100, 0.100, 0.050, 0.050]`：
- CSV文件：`H_0.300_0.200_0.100_0.100_0.050_0.050.csv`
- VDB文件：`H_0.300_0.200_0.100_0.100_0.050_0.050.vdb`

## 输出目录结构

所有结果文件统一保存在 `optimization_results` 目录下：
```
optimization_results/
├── H_0.300_0.200_0.100_0.100_0.050_0.050.csv
├── H_0.300_0.200_0.100_0.100_0.050_0.050.vdb
├── H_0.400_0.300_0.200_0.100_0.050_0.050.csv
├── H_0.400_0.300_0.200_0.100_0.050_0.050.vdb
└── ...
```

## 使用方法

### 单次运行模式
```bash
openTM.exe
```
使用默认参数运行单次优化，结果保存为基于默认目标张量的文件名。

### 批量运行模式
```bash
openTM.exe batch_params.csv
```
从CSV文件读取多组参数进行批量优化。

### CSV参数文件格式
```csv
resolution,heat_ratio_1,heat_ratio_2,target_xx,target_yy,target_zz,target_xy,target_yz,target_xz,init_way,model,output_prefix
32,1.0,0.0001,0.300,0.200,0.100,0.100,0.050,0.050,IWP,mma,test_case
64,1.0,0.0001,0.400,0.300,0.200,0.100,0.050,0.050,IWP,mma,test_case
```

## 输出文件格式

### CSV文件格式
- **无标题行**（按要求）
- 每行格式：`x,y,z,density`
- 密度值精度：6位小数

### VDB文件格式
- 标准OpenVDB格式
- 可用于Blender、Houdini等3D软件
- 支持体积渲染和网格生成

## 编译方法

### 在VSCode中编译
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 选择 "Tasks: Run Task"
3. 选择 "Build Release (MSBuild)"

### 使用命令行编译
```bash
cd build
msbuild openTM.sln /p:Configuration=Release /p:Platform=x64 /m
```

## 历史版本备份

- `main_history_v2.cpp` - 原始版本备份
- `main_batch.cpp` - 批量处理版本备份

## 技术特性

1. **自动目录创建** - 程序会自动创建输出目录
2. **错误处理** - 完善的异常处理机制
3. **文件格式支持** - 同时支持CSV和VDB两种格式
4. **精确命名** - 基于目标张量的精确文件命名
5. **批量处理** - 支持从CSV文件批量读取参数

## 注意事项

1. 确保有足够的磁盘空间存储结果文件
2. VDB文件通常比CSV文件更大
3. 文件名中的数值保留3位小数精度
4. 程序会自动覆盖同名文件

## 故障排除

### 常见问题
1. **编译错误** - 确保Visual Studio 2022和CUDA Toolkit已正确安装
2. **运行时错误** - 检查GPU驱动程序和CUDA版本兼容性
3. **文件保存失败** - 确保有写入权限和足够磁盘空间

### 调试模式
可以编译Debug版本进行详细调试：
```bash
msbuild openTM.sln /p:Configuration=Debug /p:Platform=x64 /m
```

## 性能建议

1. 对于大批量计算，建议使用较小分辨率进行初步测试
2. 定期清理输出目录以节省磁盘空间
3. 可以并行运行多个实例处理不同参数集
