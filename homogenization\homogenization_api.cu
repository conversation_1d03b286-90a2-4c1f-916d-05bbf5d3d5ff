#include "homogenization/homogenization_api.h"
#include "homogenization/Framework.cuh"

using namespace homo;

TensorResult runHomogenizationOnce(const cfg::HomoConfig& config, const std::vector<float>& rho_initial) {
    
    int reso = config.reso[0];
    int ne = pow(reso, 3);

    if (rho_initial.size() != ne) {
        throw std::runtime_error("Initial density vector size does not match resolution.");
    }

    // 1. Setup the homogenization solver
    Homogenization_H hom_H(config);
    hom_H.ConfigDiagPrecondition(0);

    // 2. Setup the density field variable
    homo::var_tsexp_t<> rho_H(reso, reso, reso);
    
    // 3. Load the initial density from the input vector
    rho_H.value().graft(const_cast<float*>(rho_initial.data()));

    // 4. Apply filtering and projection (same as in the optimization loop)
    auto rhop_H = rho_H.conv(radial_convker_t<float, Spline4>(1.5, 0)).pow(3) * (config.heatRatio[0] - config.heatRatio[1]) + config.heatRatio[1];

    // 5. Create the heat tensor object
    heat_tensor_t<float, decltype(rhop_H)> Hh(hom_H, rhop_H);

    // 6. Evaluate the tensor once
    Hh.eval();

    // 7. Extract tensor components and return as TensorResult
    TensorResult result;
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            result.H_[i][j] = Hh.H_[i][j];
        }
    }

    return result;
}