@echo off
echo Compiling openTM with batch processing support...

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64

REM Backup original main.cpp
if not exist main_original.cpp (
    echo Backing up original main.cpp...
    copy main.cpp main_original.cpp
)

REM Replace main.cpp with batch version
echo Replacing main.cpp with batch processing version...
copy main_batch.cpp main.cpp

REM Navigate to build directory
cd /d "%~dp0build"

REM Build the project
echo Building project...
msbuild openTM.sln /p:Configuration=Release /p:Platform=x64 /m

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! Batch-enabled executable is at: build\Release\openTM.exe
    echo.
    echo Usage:
    echo   Single run: openTM.exe
    echo   Batch run:   openTM.exe batch_params.csv
    echo.
) else (
    echo.
    echo Build failed with error code %ERRORLEVEL%
    echo Restoring original main.cpp...
    cd /d "%~dp0"
    copy main_original.cpp main.cpp
    echo.
)

pause
