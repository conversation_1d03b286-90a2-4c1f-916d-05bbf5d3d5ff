# OpenTM 批量计算功能使用说明

## 概述
本项目现在支持批量拓扑优化计算，可以从CSV文件读取多组参数并自动执行优化计算。

## 编译方法

### 在VSCode中编译
1. 打开VSCode，确保安装了以下扩展：
   - C/C++ Extension Pack
   - CMake Tools (可选)

2. 使用快捷键 `Ctrl+Shift+P` 打开命令面板，选择 "Tasks: Run Task"

3. 选择以下任务之一：
   - `Build Debug (MSBuild)` - 编译Debug版本
   - `Build Release (MSBuild)` - 编译Release版本
   - `Rebuild Debug` - 重新编译Debug版本
   - `Rebuild Release` - 重新编译Release版本

### 使用批处理脚本编译
运行以下脚本之一：
- `build_debug.bat` - 编译Debug版本
- `build_release.bat` - 编译Release版本
- `compile_batch.bat` - 编译支持批量处理的版本

## 使用方法

### 单次运行模式
```bash
openTM.exe
```
这将运行默认的单次优化计算。

### 批量运行模式
```bash
openTM.exe batch_params.csv
```
从CSV文件读取参数并执行批量计算。

## CSV参数文件格式

CSV文件应包含以下列（第一行为标题行）：

| 列名 | 描述 | 示例值 |
|------|------|--------|
| resolution | 网格分辨率 | 64, 128 |
| heat_ratio_1 | 材料1热传导比率 | 1.0 |
| heat_ratio_2 | 材料2热传导比率 | 0.0001 |
| target_xx | 目标张量xx分量 | 0.3 |
| target_yy | 目标张量yy分量 | 0.2 |
| target_zz | 目标张量zz分量 | 0.1 |
| target_xy | 目标张量xy分量 | 0.1 |
| target_yz | 目标张量yz分量 | 0.05 |
| target_xz | 目标张量xz分量 | 0.05 |
| init_way | 初始化方式 | IWP, random, noise |
| model | 优化算法 | mma, oc |
| output_prefix | 输出文件前缀 | test_case |

### 示例CSV文件
```csv
resolution,heat_ratio_1,heat_ratio_2,target_xx,target_yy,target_zz,target_xy,target_yz,target_xz,init_way,model,output_prefix
64,1.0,0.0001,0.3,0.2,0.1,0.1,0.05,0.05,IWP,mma,case_1
128,1.0,0.0001,0.4,0.3,0.2,0.1,0.05,0.05,IWP,mma,case_2
```

## 输出文件

每个计算案例会生成一个CSV文件，包含：
- x, y, z坐标
- 对应位置的密度值

文件命名格式：`{output_prefix}_case_{序号}.csv`

## 调试和开发

### 在VSCode中调试
1. 按 `F5` 启动调试
2. 或使用 `Ctrl+Shift+P` -> "Debug: Start Debugging"

### 修改main函数
如需修改批量处理逻辑，编辑 `main_batch.cpp` 文件，然后重新编译。

### 恢复原始版本
如果需要恢复到原始的main.cpp：
```bash
copy main_original.cpp main.cpp
```

## 常见问题

### 编译错误
1. 确保Visual Studio 2022已安装
2. 确保CUDA Toolkit已安装
3. 检查依赖库是否正确安装（Eigen3, OpenVDB等）

### 运行错误
1. 确保GPU驱动程序是最新的
2. 检查CUDA版本兼容性
3. 确保有足够的GPU内存

### CSV文件格式错误
1. 确保第一行是标题行
2. 确保所有数值列都是有效的数字
3. 确保字符串列（init_way, model）使用正确的值

## 性能优化建议

1. 对于大批量计算，建议使用较小的分辨率（如64）进行初步测试
2. 可以并行运行多个实例处理不同的参数集
3. 定期清理输出文件以节省磁盘空间
