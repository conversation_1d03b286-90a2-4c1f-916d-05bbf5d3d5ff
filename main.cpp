﻿#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <iomanip>
#include <algorithm>
#include "cmdline.h"
#include "openvdb/tools/VolumeToMesh.h"
#include "voxelIO/openvdb_wrapper_t.h"

// External function declarations
extern void cuda_test(void);
extern void testAutoDiff(void);
extern void test_MMA(cfg::HomoConfig, int mode);
extern std::vector<float> runCustom(cfg::HomoConfig config, std::vector<float>* rho0 = nullptr);
namespace homo {
	extern std::string setPathPrefix(const std::string& fprefix);
}



class wrapper_homo {
	cfg::HomoConfig config;
	std::vector<float> rho;
public:
	wrapper_homo() { config.init(); };
	~wrapper_homo() {};
	void setDensity(std::vector<float> rho_) {
		rho = rho_;
	}
	void setConfig(int reso, std::vector<double> heat_ratios, std::vector<double> target_ratio, cfg::Model model) {
		config.init();
		config.reso[0] = reso;
		config.reso[1] = reso;
		config.reso[2] = reso;

		if (heat_ratios.size() != 2) {
			std::cout << "Input wrong size of heat_ratios" << std::endl;
			exit(-1);
		}

		for (int i = 0; i < 2; i++)
			config.heatRatio[i] = heat_ratios[i];
		for (int i = 0; i < 6; i++)
			config.target_tensor[i] = target_ratio[i];

		config.winit = cfg::InitWay::IWP;
		config.model = model;
	}
	std::vector<float> optimize() {
		return runCustom(config, &rho);
	}
};

std::vector<float> runInstance(int reso, std::vector<double> heat_ratios, std::vector<double> target_ratio, cfg::InitWay initway, cfg::Model model) {
	cfg::HomoConfig config;
	config.init();
	config.reso[0] = reso;
	config.reso[1] = reso;
	config.reso[2] = reso;

	if (heat_ratios.size() != 2) {
		std::cout << "Input wrong size of heat_ratios" << std::endl;
		exit(-1);
	}

	for (int i = 0; i < 2; i++)
		config.heatRatio[i] = heat_ratios[i];
	for (int i = 0; i < 6; i++)
		config.target_tensor[i] = target_ratio[i];

	config.winit = initway;
	config.model = model;
	return runCustom(config);
}

// Function to create output directory if it doesn't exist
void createOutputDirectory(const std::string& dir_path) {
    std::string command = "mkdir \"" + dir_path + "\" 2>nul";
    system(command.c_str());
}

// Function to generate filename from target tensor
std::string generateTensorFilename(const std::vector<double>& target_tensor, const std::string& extension) {
    std::stringstream ss;
    ss << std::fixed << std::setprecision(3);
    ss << "H_" << target_tensor[0] << "_" << target_tensor[1] << "_" << target_tensor[2]
       << "_" << target_tensor[3] << "_" << target_tensor[4] << "_" << target_tensor[5]
       << extension;
    return ss.str();
}

// Function to save results to CSV (without header)
void saveResultsToCSV(const std::vector<float>& result, const std::string& filepath, int reso) {
    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot create CSV file " << filepath << std::endl;
        return;
    }

    // No header line as requested
    for (int k = 0; k < reso; k++) {
        for (int j = 0; j < reso; j++) {
            for (int i = 0; i < reso; i++) {
                int idx = k * reso * reso + j * reso + i;
                file << i << "," << j << "," << k << "," << std::fixed << std::setprecision(6) << result[idx] << std::endl;
            }
        }
    }
    file.close();
    std::cout << "CSV results saved to: " << filepath << std::endl;
}

// Function to save results to VDB format
void saveResultsToVDB(const std::vector<float>& result, const std::string& filepath, int reso) {
    try {
        int gridSize[3] = {reso, reso, reso};
        openvdb_wrapper_t<float>::lexicalGrid2openVDBfile(filepath, gridSize, result);
        std::cout << "VDB results saved to: " << filepath << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error saving VDB file " << filepath << ": " << e.what() << std::endl;
    }
}

int main(int argc, char* argv[])
{
    std::cout << "OpenTM Topology Optimization - Enhanced Version with Tensor-based Output" << std::endl;

    // Create output directory
    std::string output_dir = "optimization_results";
    createOutputDirectory(output_dir);

    try {
        if (argc > 1) {
            // Batch mode: read parameters from CSV file
            std::string input_file = argv[1];
            std::cout << "Batch processing mode - reading from: " << input_file << std::endl;

            std::ifstream file(input_file);
            if (!file.is_open()) {
                std::cerr << "Error: Cannot open file " << input_file << std::endl;
                return -1;
            }

            std::string line;
            bool first_line = true;
            int case_num = 1;

            while (std::getline(file, line)) {
                if (first_line) {
                    first_line = false;
                    continue; // Skip header
                }
                if (line.empty()) continue;

                // Simple CSV parsing
                std::stringstream ss(line);
                std::string cell;
                std::vector<std::string> row;

                while (std::getline(ss, cell, ',')) {
                    row.push_back(cell);
                }

                if (row.size() >= 12) {
                    int reso = std::stoi(row[0]);
                    std::vector<double> heat_ratios = {std::stod(row[1]), std::stod(row[2])};
                    std::vector<double> target_tensor = {
                        std::stod(row[3]), std::stod(row[4]), std::stod(row[5]),
                        std::stod(row[6]), std::stod(row[7]), std::stod(row[8])
                    };

                    cfg::InitWay init_way = cfg::InitWay::IWP;
                    if (row[9] == "random") init_way = cfg::InitWay::random;
                    else if (row[9] == "noise") init_way = cfg::InitWay::noise;

                    cfg::Model model = cfg::Model::mma;
                    if (row[10] == "oc") model = cfg::Model::oc;

                    std::cout << "\n=== Processing case " << case_num << " ===" << std::endl;
                    std::cout << "Resolution: " << reso << std::endl;
                    std::cout << "Target tensor: [" << target_tensor[0] << ", " << target_tensor[1] << ", "
                              << target_tensor[2] << ", " << target_tensor[3] << ", "
                              << target_tensor[4] << ", " << target_tensor[5] << "]" << std::endl;

                    auto result = runInstance(reso, heat_ratios, target_tensor, init_way, model);

                    // Generate filenames based on target tensor
                    std::string csv_filename = generateTensorFilename(target_tensor, ".csv");
                    std::string vdb_filename = generateTensorFilename(target_tensor, ".vdb");

                    std::string csv_filepath = output_dir + "/" + csv_filename;
                    std::string vdb_filepath = output_dir + "/" + vdb_filename;

                    // Save both CSV and VDB formats
                    saveResultsToCSV(result, csv_filepath, reso);
                    saveResultsToVDB(result, vdb_filepath, reso);

                    case_num++;
                }
            }
            file.close();
            std::cout << "\nBatch processing completed!" << std::endl;
        } else {
            // Single run mode (original behavior)
            std::cout << "Single run mode - using default parameters" << std::endl;
            std::vector<double> default_target = {0.3, 0.2, 0.1, 0.1, 0.05, 0.05};
            auto result = runInstance(128, { 1, 1e-4 }, default_target, cfg::InitWay::IWP, cfg::Model::mma);

            // Generate filenames based on target tensor
            std::string csv_filename = generateTensorFilename(default_target, ".csv");
            std::string vdb_filename = generateTensorFilename(default_target, ".vdb");

            std::string csv_filepath = output_dir + "/" + csv_filename;
            std::string vdb_filepath = output_dir + "/" + vdb_filename;

            // Save both CSV and VDB formats
            saveResultsToCSV(result, csv_filepath, 128);
            saveResultsToVDB(result, vdb_filepath, 128);
        }
    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "Unhandled Exception occurred, aborting..." << std::endl;
        return -1;
    }

    std::cout << "Program completed successfully!" << std::endl;
    std::cout << "All results saved in directory: " << output_dir << std::endl;
    return 0;
}

/* below is the code for user to bind python .pyd file*/
//#include <pybind11/pybind11.h>
//#include <pybind11/stl.h>
//namespace py = pybind11;
//PYBIND11_MODULE(openTM, m) {
//	m.doc() = "....";
//	py::enum_<cfg::InitWay>(m, "InitWay")
//		.value("random", cfg::InitWay::random)
//		.value("randcenter", cfg::InitWay::randcenter)
//		.value("noise", cfg::InitWay::noise)
//		.value("manual", cfg::InitWay::manual)
//		.value("interp", cfg::InitWay::interp)
//		.value("rep_randcenter", cfg::InitWay::rep_randcenter)
//		.value("P", cfg::InitWay::P)
//		.value("G", cfg::InitWay::G)
//		.value("D", cfg::InitWay::D)
//		.value("IWP", cfg::InitWay::IWP)
//		.value("example", cfg::InitWay::example);
//
//	py::enum_<cfg::Model>(m, "Model")
//		.value("mma", cfg::Model::mma)
//		.value("oc", cfg::Model::oc);
//	m.def("runInstance", &runInstance, py::arg("reso"), py::arg("heat_ratios"), py::arg("target_ratio"),
//		py::arg("initway") = cfg::InitWay::IWP, py::arg("model") = cfg::Model::mma);
//
//	py::class_<wrapper_homo>(m, "homo")
//		.def(py::init<>())
//		.def("setDensity", &wrapper_homo::setDensity, "set density by given rho")
//		.def("setConfig", &wrapper_homo::setConfig, py::arg("reso"), py::arg("heat_ratios"), py::arg("target_ratio"), py::arg("model") = cfg::Model::oc)
//		.def("optimize", &wrapper_homo::optimize, "optimization");
//}

///* below is the code for user to build matlab .mex64w file*/
//#include "mex.hpp"
//#include "mexAdapter.hpp"
//#include "MatlabDataArray/TypedArray.hpp"
//class MexFunction : public matlab::mex::Function {
//    std::shared_ptr<matlab::engine::MATLABEngine> matlabPtr = getEngine();
//    matlab::data::ArrayFactory factory;
//    std::ostringstream stream;
//public:
//    void operator()(matlab::mex::ArgumentList outputs, matlab::mex::ArgumentList inputs) {
//        checkArguments(outputs, inputs);
//        double trran = inputs[0][0];
//        int reso = int(trran);
//        matlab::data::TypedArray<double> heat_ratiom = std::move(inputs[1]);
//       std::vector<double> heat_ratio(2);
//        for (int i = 0; i < 2; i++)
//            heat_ratio[i] = heat_ratiom[i];
//        matlab::data::TypedArray<double> ttm = std::move(inputs[2]);
//        std::vector<double> target_tensor(6);
//        for (int i = 0; i < 6; i++)
//            target_tensor[i] = ttm[i];
//        cfg::Model model;
//        matlab::data::StringArray inArrayRef1(inputs[3]);
//        std::string tstring = (std::string)inArrayRef1[0];
//        if (tstring == "mma")
//            model = cfg::Model::mma;
//        else if (tstring == "oc")
//            model = cfg::Model::oc;
//        std::vector<float> result;
//        if (inputs.size() < 4)
//            result = runInstance(reso,heat_ratio,target_tensor,cfg::InitWay::IWP,model);
//        else {
//            std::vector<float> _rho(reso*reso*reso);
//            if (inputs[4].getNumberOfElements() != pow(reso, 3)) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Scale of rho wrong!") }));
//            }
//            else {
//                matlab::data::TypedArray<double> rhom = std::move(inputs[2]);
//                for (int i = 0; i < reso * reso * reso; i++) {
//                    _rho[i] = rhom[i];
//                }
//            }
//            wrapper_homo homo;
//            homo.setConfig(reso, heat_ratio, target_tensor, model);
//            homo.setDensity(_rho);
//            homo.optimize();
//        }
//        const float* start = &result[0];
//        const float* end = &result[reso * reso * reso];
//        unsigned long long t = reso * reso * reso;
//        outputs[0] = factory.createArray<float>({1, t}, start, end);
//    }
//    void displayOnMATLAB(std::ostringstream& stream) {
//        // Pass stream content to MATLAB fprintf function
//        matlabPtr->feval(u"fprintf", 0,
//            std::vector<matlab::data::Array>({ factory.createScalar(stream.str()) }));
//        // Clear stream buffer
//        stream.str("");
//    }
//    void checkArguments(matlab::mex::ArgumentList outputs, matlab::mex::ArgumentList inputs) {
//        std::shared_ptr<matlab::engine::MATLABEngine> matlabPtr = getEngine();
//        matlab::data::ArrayFactory factory;
//
//        if (inputs.size() < 3 || inputs.size() > 5) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Two inputs required") }));
//        }
//
//        if (inputs[0].getNumberOfElements() != 1) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Input resolution must be a scalar") }));
//        }
//
//        if (inputs[0].getType() != matlab::data::ArrayType::DOUBLE) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Input resolution must be a integer") }));
//        }
//
//        if (inputs[1].getNumberOfElements() != 2) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Input heat ratios of black & white 2 materials") }));
//        }
//        if (inputs[1].getType() != matlab::data::ArrayType::DOUBLE ||
//            inputs[1].getType() == matlab::data::ArrayType::COMPLEX_DOUBLE) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Heat ratios be type double") }));
//        }
//
//        if (inputs[2].getNumberOfElements() != 6) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Aimed tensor [xx yy zz xy yz xz] is needed") }));
//        }
//        if (inputs[2].getType() != matlab::data::ArrayType::DOUBLE ||
//            inputs[2].getType() == matlab::data::ArrayType::COMPLEX_DOUBLE) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Aimed tensor be type double") }));
//        }
//        if (inputs.size() > 3) {
//            if (inputs[3].getNumberOfElements() != 1) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Optimizer need to be a string use \"\" may help") }));
//            }
//            matlab::data::StringArray inArrayRef1(inputs[3]);
//            std::string tstring = (std::string)inArrayRef1[0];
//            std::vector<std::string> ienum = { "oc", "mma"};
//            if (inputs[3].getType() != matlab::data::ArrayType::MATLAB_STRING || std::find(ienum.begin(), ienum.end(), tstring) == ienum.end()) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Optimizer type error") }));
//            }
//        }
//        if (inputs.size() > 4) {
//            if (inputs[4].getType() != matlab::data::ArrayType::DOUBLE ||
//                inputs[4].getType() == matlab::data::ArrayType::COMPLEX_DOUBLE) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Aimed tensor be type double") }));
//            }
//        }
//    }
//};

/* below is the code for user to bind python .pyd file*/
//#include <pybind11/pybind11.h>
//#include <pybind11/stl.h>
//namespace py = pybind11;
//PYBIND11_MODULE(openTM, m) {
//	m.doc() = "....";
//	py::enum_<cfg::InitWay>(m, "InitWay")
//		.value("random", cfg::InitWay::random)
//		.value("randcenter", cfg::InitWay::randcenter)
//		.value("noise", cfg::InitWay::noise)
//		.value("manual", cfg::InitWay::manual)
//		.value("interp", cfg::InitWay::interp)
//		.value("rep_randcenter", cfg::InitWay::rep_randcenter)
//		.value("P", cfg::InitWay::P)
//		.value("G", cfg::InitWay::G)
//		.value("D", cfg::InitWay::D)
//		.value("IWP", cfg::InitWay::IWP)
//		.value("example", cfg::InitWay::example);
//
//	py::enum_<cfg::Model>(m, "Model")
//		.value("mma", cfg::Model::mma)
//		.value("oc", cfg::Model::oc);
//	m.def("runInstance", &runInstance, py::arg("reso"), py::arg("heat_ratios"), py::arg("target_ratio"),
//		py::arg("initway") = cfg::InitWay::IWP, py::arg("model") = cfg::Model::mma);
//
//	py::class_<wrapper_homo>(m, "homo")
//		.def(py::init<>())
//		.def("setDensity", &wrapper_homo::setDensity, "set density by given rho")
//		.def("setConfig", &wrapper_homo::setConfig, py::arg("reso"), py::arg("heat_ratios"), py::arg("target_ratio"), py::arg("model") = cfg::Model::oc)
//		.def("optimize", &wrapper_homo::optimize, "optimization");
//}

///* below is the code for user to build matlab .mexw64 file*/
//#include "mex.hpp"
//#include "mexAdapter.hpp"
//
//class MexFunction : public matlab::mex::Function {
//    matlab::data::ArrayFactory factory;
//public:
//    void operator()(matlab::mex::ArgumentList outputs, matlab::mex::ArgumentList inputs) {
//        checkArguments(outputs, inputs);
//        matlab::data::TypedArray<double> resom = std::move(inputs[0]);
//        int reso = resom[0];
//        matlab::data::TypedArray<double> hrm = std::move(inputs[1]);
//        std::vector<double> heat_ratio(2);
//        for (int i = 0; i < 2; i++)
//            heat_ratio[i] = hrm[i];
//        matlab::data::TypedArray<double> ttm = std::move(inputs[2]);
//        std::vector<double> target_tensor(6);
//        for (int i = 0; i < 6; i++)
//            target_tensor[i] = ttm[i];
//        cfg::Model model;
//        matlab::data::StringArray inArrayRef1(inputs[3]);
//        std::string tstring = (std::string)inArrayRef1[0];
//        if (tstring == "mma")
//            model = cfg::Model::mma;
//        else if (tstring == "oc")
//            model = cfg::Model::oc;
//        std::vector<float> result;
//        if (inputs.size() < 4)
//            result = runInstance(reso,heat_ratio,target_tensor,cfg::InitWay::IWP,model);
//        else {
//            std::vector<float> _rho(reso*reso*reso);
//            if (inputs[4].getNumberOfElements() != pow(reso, 3)) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Scale of rho wrong!") }));
//            }
//            else {
//                matlab::data::TypedArray<double> rhom = std::move(inputs[2]);
//                for (int i = 0; i < reso * reso * reso; i++) {
//                    _rho[i] = rhom[i];
//                }
//            }
//            wrapper_homo homo;
//            homo.setConfig(reso, heat_ratio, target_tensor, model);
//            homo.setDensity(_rho);
//            homo.optimize();
//        }
//        const float* start = &result[0];
//        const float* end = &result[reso * reso * reso];
//        unsigned long long t = reso * reso * reso;
//        outputs[0] = factory.createArray<float>({1, t}, start, end);
//    }
//    void displayOnMATLAB(std::ostringstream& stream) {
//        // Pass stream content to MATLAB fprintf function
//        matlabPtr->feval(u"fprintf", 0,
//            std::vector<matlab::data::Array>({ factory.createScalar(stream.str()) }));
//        // Clear stream buffer
//        stream.str("");
//    }
//    void checkArguments(matlab::mex::ArgumentList outputs, matlab::mex::ArgumentList inputs) {
//        std::shared_ptr<matlab::engine::MATLABEngine> matlabPtr = getEngine();
//        matlab::data::ArrayFactory factory;
//
//        if (inputs.size() < 3 || inputs.size() > 5) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Two inputs required") }));
//        }
//
//        if (inputs[0].getNumberOfElements() != 1) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Input resolution must be a scalar") }));
//        }
//
//        if (inputs[0].getType() != matlab::data::ArrayType::DOUBLE) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Input resolution must be a integer") }));
//        }
//
//        if (inputs[1].getNumberOfElements() != 2) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Input heat ratios of black & white 2 materials") }));
//        }
//        if (inputs[1].getType() != matlab::data::ArrayType::DOUBLE ||
//            inputs[1].getType() == matlab::data::ArrayType::COMPLEX_DOUBLE) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Heat ratios be type double") }));
//        }
//
//        if (inputs[2].getNumberOfElements() != 6) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Aimed tensor [xx yy zz xy yz xz] is needed") }));
//        }
//        if (inputs[2].getType() != matlab::data::ArrayType::DOUBLE ||
//            inputs[2].getType() == matlab::data::ArrayType::COMPLEX_DOUBLE) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Aimed tensor be type double") }));
//        }
//        if (inputs.size() > 3) {
//            if (inputs[3].getNumberOfElements() != 1) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Optimizer need to be a string use \"\" may help") }));
//            }
//            matlab::data::StringArray inArrayRef1(inputs[3]);
//            std::string tstring = (std::string)inArrayRef1[0];
//            std::vector<std::string> ienum = { "oc", "mma"};
//            if (inputs[3].getType() != matlab::data::ArrayType::MATLAB_STRING || std::find(ienum.begin(), ienum.end(), tstring) == ienum.end()) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Optimizer type error") }));
//            }
//        }
//        if (inputs.size() > 4) {
//            if (inputs[4].getType() != matlab::data::ArrayType::DOUBLE ||
//                inputs[4].getType() == matlab::data::ArrayType::COMPLEX_DOUBLE) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Aimed tensor be type double") }));
//            }
//        }
//    }
//};

/* below is the code for user to bind python .pyd file*/
//#include <pybind11/pybind11.h>
//#include <pybind11/stl.h>
//namespace py = pybind11;
//PYBIND11_MODULE(openTM, m) {
//	m.doc() = "....";
//	py::enum_<cfg::InitWay>(m, "InitWay")
//		.value("random", cfg::InitWay::random)
//		.value("randcenter", cfg::InitWay::randcenter)
//		.value("noise", cfg::InitWay::noise)
//		.value("manual", cfg::InitWay::manual)
//		.value("interp", cfg::InitWay::interp)
//		.value("rep_randcenter", cfg::InitWay::rep_randcenter)
//		.value("P", cfg::InitWay::P)
//		.value("G", cfg::InitWay::G)
//		.value("D", cfg::InitWay::D)
//		.value("IWP", cfg::InitWay::IWP)
//		.value("example", cfg::InitWay::example);
//
//	py::enum_<cfg::Model>(m, "Model")
//		.value("mma", cfg::Model::mma)
//		.value("oc", cfg::Model::oc);
//	m.def("runInstance", &runInstance, py::arg("reso"), py::arg("heat_ratios"), py::arg("target_ratio"),
//		py::arg("initway") = cfg::InitWay::IWP, py::arg("model") = cfg::Model::mma);
//
//	py::class_<wrapper_homo>(m, "homo")
//		.def(py::init<>())
//		.def("setDensity", &wrapper_homo::setDensity, "set density by given rho")
//		.def("setConfig", &wrapper_homo::setConfig, py::arg("reso"), py::arg("heat_ratios"), py::arg("target_ratio"), py::arg("model") = cfg::Model::oc)
//		.def("optimize", &wrapper_homo::optimize, "optimization");
//}

///* below is the code for user to build matlab .mexw64 file*/
//#include "mex.hpp"
//#include "mexAdapter.hpp"
//#include "MatlabDataArray/TypedArray.hpp"
//class MexFunction : public matlab::mex::Function {
//    std::shared_ptr<matlab::engine::MATLABEngine> matlabPtr = getEngine();
//    matlab::data::ArrayFactory factory;
//    std::ostringstream stream;
//public:
//    void operator()(matlab::mex::ArgumentList outputs, matlab::mex::ArgumentList inputs) {
//        checkArguments(outputs, inputs);
//        double trran = inputs[0][0];
//        int reso = int(trran);
//        matlab::data::TypedArray<double> heat_ratiom = std::move(inputs[1]);
//       std::vector<double> heat_ratio(2);
//        for (int i = 0; i < 2; i++)
//            heat_ratio[i] = heat_ratiom[i];
//        matlab::data::TypedArray<double> ttm = std::move(inputs[2]);
//        std::vector<double> target_tensor(6);
//        for (int i = 0; i < 6; i++)
//            target_tensor[i] = ttm[i];
//        cfg::Model model;
//        matlab::data::StringArray inArrayRef1(inputs[3]);
//        std::string tstring = (std::string)inArrayRef1[0];
//        if (tstring == "mma")
//            model = cfg::Model::mma;
//        else if (tstring == "oc")
//            model = cfg::Model::oc;
//        std::vector<float> result;
//        if (inputs.size() < 4)
//            result = runInstance(reso,heat_ratio,target_tensor,cfg::InitWay::IWP,model);
//        else {
//            std::vector<float> _rho(reso*reso*reso);
//            if (inputs[4].getNumberOfElements() != pow(reso, 3)) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Scale of rho wrong!") }));
//            }
//            else {
//                matlab::data::TypedArray<double> rhom = std::move(inputs[2]);
//                for (int i = 0; i < reso * reso * reso; i++) {
//                    _rho[i] = rhom[i];
//                }
//            }
//            wrapper_homo homo;
//            homo.setConfig(reso, heat_ratio, target_tensor, model);
//            homo.setDensity(_rho);
//            homo.optimize();
//        }
//        const float* start = &result[0];
//        const float* end = &result[reso * reso * reso];
//        unsigned long long t = reso * reso * reso;
//        outputs[0] = factory.createArray<float>({1, t}, start, end);
//    }
//    void displayOnMATLAB(std::ostringstream& stream) {
//        // Pass stream content to MATLAB fprintf function
//        matlabPtr->feval(u"fprintf", 0,
//            std::vector<matlab::data::Array>({ factory.createScalar(stream.str()) }));
//        // Clear stream buffer
//        stream.str("");
//    }
//    void checkArguments(matlab::mex::ArgumentList outputs, matlab::mex::ArgumentList inputs) {
//        std::shared_ptr<matlab::engine::MATLABEngine> matlabPtr = getEngine();
//        matlab::data::ArrayFactory factory;
//
//        if (inputs.size() < 3 || inputs.size() > 5) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Two inputs required") }));
//        }
//
//        if (inputs[0].getNumberOfElements() != 1) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Input resolution must be a scalar") }));
//        }
//
//        if (inputs[0].getType() != matlab::data::ArrayType::DOUBLE) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Input resolution must be a integer") }));
//        }
//
//        if (inputs[1].getNumberOfElements() != 2) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Input heat ratios of black & white 2 materials") }));
//        }
//        if (inputs[1].getType() != matlab::data::ArrayType::DOUBLE ||
//            inputs[1].getType() == matlab::data::ArrayType::COMPLEX_DOUBLE) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Heat ratios be type double") }));
//        }
//
//        if (inputs[2].getNumberOfElements() != 6) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Aimed tensor [xx yy zz xy yz xz] is needed") }));
//        }
//        if (inputs[2].getType() != matlab::data::ArrayType::DOUBLE ||
//            inputs[2].getType() == matlab::data::ArrayType::COMPLEX_DOUBLE) {
//            matlabPtr->feval(u"error",
//                0, std::vector<matlab::data::Array>({ factory.createScalar("Aimed tensor be type double") }));
//        }
//        if (inputs.size() > 3) {
//            if (inputs[3].getNumberOfElements() != 1) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Optimizer need to be a string use \"\" may help") }));
//            }
//            matlab::data::StringArray inArrayRef1(inputs[3]);
//            std::string tstring = (std::string)inArrayRef1[0];
//            std::vector<std::string> ienum = { "oc", "mma"};
//            if (inputs[3].getType() != matlab::data::ArrayType::MATLAB_STRING || std::find(ienum.begin(), ienum.end(), tstring) == ienum.end()) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Optimizer type error") }));
//            }
//        }
//        if (inputs.size() > 4) {
//            if (inputs[4].getType() != matlab::data::ArrayType::DOUBLE ||
//                inputs[4].getType() == matlab::data::ArrayType::COMPLEX_DOUBLE) {
//                matlabPtr->feval(u"error",
//                    0, std::vector<matlab::data::Array>({ factory.createScalar("Aimed tensor be type double") }));
//            }
//        }
//    }
//};