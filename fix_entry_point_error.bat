@echo off
echo ========================================
echo OpenTM Entry Point Error Fix
echo ========================================
echo.

cd /d "%~dp0"

echo Step 1: Clean up conflicting TBB libraries
echo.

echo Current DLL files in Release directory:
dir "build\Release\*.dll" /b
echo.

echo Removing old/conflicting TBB versions...
if exist "build\Release\tbb.dll" (
    echo Removing tbb.dll (old version)...
    del "build\Release\tbb.dll"
)

if exist "build\Release\tbb_debug.dll" (
    echo Removing tbb_debug.dll (debug version)...
    del "build\Release\tbb_debug.dll"
)

echo Keeping only tbb12.dll (latest version)
echo.

echo Step 2: Check CUDA DLL versions
echo.

echo Checking CUDA DLL version consistency...
echo Current CUDA DLLs:
dir "build\Release\cu*.dll" /b
echo.

echo Note: Make sure all CUDA DLLs are from the same CUDA version (12.x)
echo If you see version mismatches, you may need to reinstall CUDA Toolkit
echo.

echo Step 3: Set up proper environment
echo.

echo Setting up environment variables...
set "PATH=%CD%\build\Release;%PATH%"
echo Added Release directory to PATH

echo.
echo Step 4: Test the application
echo.

cd build\Release

echo Testing openTM.exe...
echo.

echo Attempting to run with help flag...
openTM.exe --help 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ Application help works!
    goto :test_basic
) else (
    echo ⚠ Help flag failed, trying basic execution...
)

:test_basic
echo.
echo Attempting basic execution...
echo This may take a moment...

timeout /t 2 /nobreak >nul

echo Running: openTM.exe
openTM.exe 2>&1
set EXIT_CODE=%ERRORLEVEL%

echo.
echo Exit code: %EXIT_CODE%

if %EXIT_CODE% EQU 0 (
    echo ✓ SUCCESS: Application ran successfully!
) else (
    if %EXIT_CODE% EQU -1073741515 (
        echo ✗ ERROR: Entry Point Not Found (0xC0000139)
        echo.
        echo Possible solutions:
        echo 1. Install Visual C++ Redistributable 2019/2022 x64
        echo 2. Install/Reinstall CUDA Toolkit 12.x
        echo 3. Check Windows Event Viewer for detailed error info
        echo 4. Try running: sfc /scannow (as administrator)
    ) else (
        echo ✗ ERROR: Application failed with exit code %EXIT_CODE%
    )
)

echo.
echo Step 5: Additional diagnostics
echo.

echo Checking system dependencies...
echo.

echo Visual C++ Runtime check:
where vcruntime140.dll >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Visual C++ Runtime found
) else (
    echo ✗ Visual C++ Runtime missing - install VC++ Redistributable
)

echo.
echo CUDA Runtime check:
if exist "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA" (
    echo ✓ CUDA Toolkit installation found
) else (
    echo ✗ CUDA Toolkit not found in standard location
)

echo.
echo OpenVDB dependencies check:
if exist "openvdb.dll" (
    echo ✓ OpenVDB DLL present
) else (
    echo ✗ OpenVDB DLL missing
)

echo.
echo ========================================
echo Fix script completed
echo ========================================
echo.

if %EXIT_CODE% NEQ 0 (
    echo If the error persists, please:
    echo 1. Check Windows Event Viewer ^(Windows Logs ^> Application^)
    echo 2. Look for error details around the time you ran openTM.exe
    echo 3. Install latest Visual C++ Redistributable from Microsoft
    echo 4. Ensure CUDA 12.x is properly installed
    echo.
)

pause
