@echo off
echo ========================================
echo Testing OpenTM after TBB fix
echo ========================================
echo.

cd /d "%~dp0\build\Release"

echo Current directory: %CD%
echo.

echo Checking TBB files (should only show tbb12.dll):
dir tbb*.dll 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo No TBB files found - this might be the issue
) else (
    echo TBB files found above
)
echo.

echo All DLL files in Release directory:
dir *.dll /b
echo.

echo Testing openTM.exe...
echo ========================================
echo.

REM Set the current directory in PATH to ensure DLLs are found
set "PATH=%CD%;%PATH%"

echo Running: openTM.exe
echo.

REM Try to run the application
openTM.exe 2>&1
set EXIT_CODE=%ERRORLEVEL%

echo.
echo ========================================
echo Exit code: %EXIT_CODE%

if %EXIT_CODE% EQU 0 (
    echo ✓ SUCCESS: Application ran successfully!
) else (
    if %EXIT_CODE% EQU -1073741515 (
        echo ✗ Still getting Entry Point Not Found error
        echo.
        echo This suggests the issue might be:
        echo 1. Missing Visual C++ Redistributable
        echo 2. CUDA version mismatch ^(note: curand64_10.dll vs other v12 DLLs^)
        echo 3. System-level DLL conflicts
    ) else (
        echo ✗ Different error with exit code: %EXIT_CODE%
    )
)

echo.
echo Additional diagnostics:
echo.

echo Checking for Visual C++ Runtime:
where vcruntime140.dll >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ vcruntime140.dll found in system
) else (
    echo ✗ vcruntime140.dll not found - install VC++ Redistributable
)

echo.
echo Checking CUDA DLL versions:
echo curand64_10.dll ^(CUDA 10.x^) - POTENTIAL VERSION CONFLICT
echo cudart64_12.dll ^(CUDA 12.x^)
echo cublas64_12.dll ^(CUDA 12.x^)
echo.
echo Recommendation: Replace curand64_10.dll with curand64_12.dll

echo.
pause
