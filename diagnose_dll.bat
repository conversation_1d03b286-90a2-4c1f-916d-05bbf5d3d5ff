@echo off
echo ========================================
echo OpenTM DLL Dependency Diagnosis
echo ========================================
echo.

echo Current directory: %CD%
echo.

echo Checking if openTM.exe exists...
if exist "build\Release\openTM.exe" (
    echo ✓ openTM.exe found
) else (
    echo ✗ openTM.exe not found
    goto :end
)
echo.

echo Checking CUDA installation...
where nvcc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ CUDA found
    nvcc --version
) else (
    echo ⚠ CUDA not found in PATH
)
echo.

echo Checking Visual C++ Redistributables...
where vcruntime140.dll >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Visual C++ Runtime found
) else (
    echo ⚠ Visual C++ Runtime may be missing
)
echo.

echo DLL files in Release directory:
dir "build\Release\*.dll" /b
echo.

echo Attempting to run openTM.exe with error capture...
cd build\Release
echo Running: openTM.exe
openTM.exe 2>&1
echo Exit code: %ERRORLEVEL%

:end
echo.
echo Diagnosis complete.
pause
