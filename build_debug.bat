@echo off
echo Building openTM in Debug mode...

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64

REM Navigate to build directory
cd /d "%~dp0build"

REM Build the project
msbuild openTM.sln /p:Configuration=Debug /p:Platform=x64 /m

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! Debug executable is at: build\Debug\openTM.exe
    echo.
) else (
    echo.
    echo Build failed with error code %ERRORLEVEL%
    echo.
)

pause
