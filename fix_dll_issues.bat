@echo off
echo ========================================
echo OpenTM DLL Issues Fix Script
echo ========================================
echo.

cd /d "%~dp0"

echo Solution 1: Remove conflicting TBB libraries
echo.

echo Current TBB files:
dir "build\Release\tbb*.dll" /b 2>nul
echo.

echo Keeping only tbb12.dll (newest version)...
if exist "build\Release\tbb.dll" (
    echo Removing old tbb.dll...
    del "build\Release\tbb.dll"
    echo ✓ Removed tbb.dll
)

if exist "build\Release\tbb_debug.dll" (
    echo Removing debug tbb_debug.dll...
    del "build\Release\tbb_debug.dll"
    echo ✓ Removed tbb_debug.dll
)
echo.

echo Solution 2: Check and copy missing CUDA DLLs
echo.

echo Looking for CUDA installation...
set CUDA_PATH_FOUND=0

if exist "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA" (
    for /d %%i in ("C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v*") do (
        set "CUDA_PATH=%%i"
        set CUDA_PATH_FOUND=1
    )
)

if %CUDA_PATH_FOUND%==1 (
    echo Found CUDA at: %CUDA_PATH%
    echo Checking for required CUDA DLLs...
    
    set "CUDA_BIN=%CUDA_PATH%\bin"
    
    if exist "%CUDA_BIN%\cudart64_12.dll" (
        echo ✓ cudart64_12.dll found
    ) else (
        echo ⚠ cudart64_12.dll missing
    )
    
    if exist "%CUDA_BIN%\curand64_10.dll" (
        echo ✓ curand64_10.dll found
    ) else (
        echo ⚠ curand64_10.dll missing
    )
    
) else (
    echo ⚠ CUDA installation not found
    echo Please install CUDA Toolkit 12.x
)
echo.

echo Solution 3: Test the application
echo.
echo Attempting to run openTM.exe...
cd build\Release

echo Testing with minimal parameters...
openTM.exe --help 2>error_log.txt
set EXIT_CODE=%ERRORLEVEL%

if %EXIT_CODE% EQU 0 (
    echo ✓ Application started successfully!
) else (
    echo ✗ Application failed with exit code: %EXIT_CODE%
    echo Error details:
    if exist error_log.txt (
        type error_log.txt
    )
)

echo.
echo Fix script completed.
echo.
echo If issues persist, try:
echo 1. Reinstall Visual C++ Redistributable 2019/2022
echo 2. Reinstall CUDA Toolkit 12.x
echo 3. Check Windows Event Viewer for detailed error information
echo.
pause
