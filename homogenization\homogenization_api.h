#pragma once
#include "homogenization/homogenization.h"
#include "homogenization/homoExpression.h"
#include "AutoDiff/TensorExpression.h"
#include "cmdline.h"
#include <vector>

// Simple struct to hold tensor results
struct TensorResult {
    float H_[3][3];
};

// API function to run homogenization for a given density field and return the resulting tensor.
// This function is designed to be called by external applications.
TensorResult runHomogenizationOnce(const cfg::HomoConfig& config, const std::vector<float>& rho_initial);