#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <iomanip>
#include <cstdlib>
#include "homogenization/homogenization_api.h"

// Function to read CSV file and return density data
std::vector<float> readCSVDensity(const std::string& filepath, int reso) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot open CSV file " << filepath << std::endl;
        return {};
    }

    std::vector<float> density_data(reso * reso * reso, 0.0f);
    std::string line;
    
    while (std::getline(file, line)) {
        if (line.empty()) continue;
        
        std::stringstream ss(line);
        std::string cell;
        std::vector<std::string> row;
        
        while (std::getline(ss, cell, ',')) {
            row.push_back(cell);
        }
        
        if (row.size() >= 4) {
            int i = std::stoi(row[0]);
            int j = std::stoi(row[1]);
            int k = std::stoi(row[2]);
            float density = std::stof(row[3]);
            
            // Convert i,j,k to linear index
            if (i >= 0 && i < reso && j >= 0 && j < reso && k >= 0 && k < reso) {
                int idx = k * reso * reso + j * reso + i;
                density_data[idx] = density;
            }
        }
    }
    
    file.close();
    return density_data;
}

// Function to apply threshold filtering to density data
std::vector<float> applyThreshold(const std::vector<float>& density_data, float threshold) {
    std::vector<float> filtered_data;
    filtered_data.reserve(density_data.size());
    
    for (float density : density_data) {
        filtered_data.push_back(density > threshold ? 1.0f : 1e-6f);
    }
    
    return filtered_data;
}

int main(int argc, char* argv[]) {
    // Check command line arguments
    if (argc < 3) {
        std::cerr << "Usage: " << argv[0] << " <density_file> <resolution> [heat_ratio1] [heat_ratio2] [threshold]" << std::endl;
        std::cerr << "  density_file: Path to the input density CSV file" << std::endl;
        std::cerr << "  resolution: Resolution of the grid (e.g., 128)" << std::endl;
        std::cerr << "  heat_ratio1: Heat ratio of material 1 (solid), default: 1.0" << std::endl;
        std::cerr << "  heat_ratio2: Heat ratio of material 2 (void), default: 1e-4" << std::endl;
        std::cerr << "  threshold: Threshold for binarization, default: 0.5" << std::endl;
        return -1;
    }

    try {
        // 1. Get parameters from command line
        std::string density_filepath = argv[1];
        int reso = std::atoi(argv[2]);
        double hr1 = (argc > 3) ? std::atof(argv[3]) : 1.0;
        double hr2 = (argc > 4) ? std::atof(argv[4]) : 1e-4;
        float threshold = (argc > 5) ? std::atof(argv[5]) : 0.5f;

        if (reso <= 0) {
            std::cerr << "Error: Invalid resolution " << reso << std::endl;
            return -1;
        }

        std::cerr << "Reading density data from: " << density_filepath << std::endl;
        std::cerr << "Resolution: " << reso << "x" << reso << "x" << reso << std::endl;
        std::cerr << "Heat ratios: " << hr1 << ", " << hr2 << std::endl;
        std::cerr << "Threshold: " << threshold << std::endl;

        // 2. Read density data from CSV
        auto density_data = readCSVDensity(density_filepath, reso);
        if (density_data.empty()) {
            std::cerr << "Error: Failed to read density data" << std::endl;
            return -1;
        }

        // 3. Apply threshold filtering
        auto filtered_density = applyThreshold(density_data, threshold);

        // 4. Setup homogenization configuration
        cfg::HomoConfig config;
        config.init();
        config.reso[0] = config.reso[1] = config.reso[2] = reso;
        config.heatRatio[0] = hr1;
        config.heatRatio[1] = hr2;

        std::cerr << "Running homogenization calculation..." << std::endl;

        // 5. Run homogenization calculation
        auto tensor_result = runHomogenizationOnce(config, filtered_density);

        // 6. Extract tensor components and output
        // The tensor_result should be a heat_tensor_t with H_[3][3] components
        std::cout << std::fixed << std::setprecision(6);
        std::cout << tensor_result.H_[0][0] << std::endl;  // Kxx
        std::cout << tensor_result.H_[1][1] << std::endl;  // Kyy  
        std::cout << tensor_result.H_[2][2] << std::endl;  // Kzz
        std::cout << tensor_result.H_[0][1] << std::endl;  // Kxy
        std::cout << tensor_result.H_[1][2] << std::endl;  // Kyz
        std::cout << tensor_result.H_[0][2] << std::endl;  // Kxz

        std::cerr << "Homogenization calculation completed successfully!" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return -1;
    }

    return 0;
}
