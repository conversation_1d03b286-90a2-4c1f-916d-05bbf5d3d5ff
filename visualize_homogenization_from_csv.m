function visualize_homogenization_from_csv(csv_filepath, threshold)
    % visualize_homogenization_from_csv - 读取、阈值化、可视化并重新计算拓扑优化结果。
    %
    % 此函数现在还支持对密度场进行阈值处理，并调用外部 C++ 程序
    % 来计算二值化后结构的等效导热张量。
    %
    % 用法:
    %   visualize_homogenization_from_csv('path/to/result.csv', 0.5)
    %
    % 参数:
    %   csv_filepath - (字符串) 指向 C++ 程序生成的 CSV 文件的路径。
    %   threshold    - (可选, 双精度) 用于二值化的阈值，默认为 0.5。
    
    % 1. 输入参数检查
    % -------------------------------------------------
    if nargin < 1
        [file, path] = uigetfile('*.csv', '请选择一个 CSV 结果文件');
        if isequal(file, 0)
            disp('用户取消了操作。');
            return;
        end
        csv_filepath = fullfile(path, file);
    end
    
    if nargin < 2
        threshold = 0.5; % 默认阈值
    end
    
    if ~ischar(csv_filepath) && ~isstring(csv_filepath)
        error('文件路径必须是一个字符串。');
    end
    
    if ~exist(csv_filepath, 'file')
        error('找不到指定的文件: %s', csv_filepath);
    end
    
    % 2. 读取和解析 CSV 文件
    % -------------------------------------------------
    fprintf('正在从 %s 读取数据...\n', csv_filepath);
    
    try
        data = readmatrix(csv_filepath);
    catch ME
        fprintf('读取 CSV 文件时出错。\n');
        fprintf('请确保文件格式为: i,j,k,density\n');
        rethrow(ME);
    end
    
    if size(data, 2) ~= 4
        error('CSV 文件格式不正确。应包含 4 列: i, j, k, density。');
    end
    
    fprintf('数据读取成功，共 %d 个点。\n', size(data, 1));
    
    % 3. 将数据重塑为 3D 矩阵
    % -------------------------------------------------
    coords = data(:, 1:3) + 1; % CSV 是 0-based, MATLAB 是 1-based
    densities = data(:, 4);
    reso = max(coords, [], 1);
    grid_size = max(reso);
    fprintf('推断出的分辨率为: %d x %d x %d\n', reso(1), reso(2), reso(3));
    rho_3d = zeros(grid_size, grid_size, grid_size);
    indices = sub2ind(size(rho_3d), coords(:, 1), coords(:, 2), coords(:, 3));
    rho_3d(indices) = densities;
    
    % 4. 对密度场进行阈值处理
    % -------------------------------------------------
    fprintf('正在对密度场进行阈值处理 (阈值 = %.2f)...\n', threshold);
    rho_binary = double(rho_3d > threshold);
    
    % 5. 保存二值化后的密度场为新的 CSV 文件
    % -------------------------------------------------
    [filepath, name, ~] = fileparts(csv_filepath);
    binary_csv_filename = fullfile(filepath, [name, '_binary.csv']);
    fprintf('正在将二值化结果保存到: %s\n', binary_csv_filename);
    
    % 将 3D 矩阵转换回 i,j,k,density 格式
    [I, J, K] = ind2sub(size(rho_binary), find(rho_binary > 0));
    binary_data_to_save = [I-1, J-1, K-1, ones(length(I), 1)]; % 转换为 0-based
    writematrix(binary_data_to_save, binary_csv_filename, 'Delimiter', ',');
    fprintf('二值化结果保存成功。\n');
    
    % 6. 可视化 3D 结果
    % -------------------------------------------------
    figure('Name', '拓扑优化结果对比', 'NumberTitle', 'off', 'Color', 'w', 'Position', [100, 100, 1200, 500]);
    subplot(1, 2, 1);
    p1 = patch(isosurface(rho_3d, threshold));
    p1.FaceColor = [0.2, 0.5, 0.9]; p1.EdgeColor = 'none';
    daspect([1, 1, 1]); view(3); axis tight; camlight; lighting gouraud;
    title(sprintf('原始结果 (iso-value = %.2f)', threshold));
    xlabel('X'); ylabel('Y'); zlabel('Z'); grid on;
    
    subplot(1, 2, 2);
    p2 = patch(isosurface(rho_binary, 0.5));
    p2.FaceColor = [0.9, 0.5, 0.2]; p2.EdgeColor = 'none';
    daspect([1, 1, 1]); view(3); axis tight; camlight; lighting gouraud;
    title('二值化结果 (0-1 分布)');
    xlabel('X'); ylabel('Y'); zlabel('Z'); grid on;
    
    % 7. 调用 C++ 程序计算二值化后的等效张量
    % -------------------------------------------------
    fprintf('\n开始调用 C++ 程序计算二值化结构的等效张量...\n');
    
    % 假设 calculate_tensor.exe 位于 build/Release/ 目录下
    % 您需要根据您的实际构建输出调整此路径
    executable_path = fullfile(pwd, 'build', 'Release', 'calculate_tensor.exe');
    if ~exist(executable_path, 'file')
        executable_path = 'calculate_tensor.exe'; % 如果不在子目录，则尝试当前目录
    end
    
    command = sprintf('"%s" --density_file "%s" --reso %d', ...
                      executable_path, binary_csv_filename, grid_size);
                      
    fprintf('执行命令: %s\n', command);
    
    [status, cmdout] = system(command);
    
    if status ~= 0
        fprintf('\n错误: C++ 程序执行失败。\n');
        fprintf('返回状态: %d\n', status);
        fprintf('程序输出:\n%s\n', cmdout);
        error('无法计算等效张量。请检查 C++ 程序路径或其依赖项。');
    end
    
    tensor_components = sscanf(cmdout, '%f');
    
    if length(tensor_components) == 6
        fprintf('\n计算成功！\n');
        fprintf('--------------------------------------------------\n');
        fprintf('二值化结构下的等效导热张量:\n');
        fprintf('  Kxx: %.6f\n', tensor_components(1));
        fprintf('  Kyy: %.6f\n', tensor_components(2));
        fprintf('  Kzz: %.6f\n', tensor_components(3));
        fprintf('  Kxy: %.6f\n', tensor_components(4));
        fprintf('  Kyz: %.6f\n', tensor_components(5));
        fprintf('  Kxz: %.6f\n', tensor_components(6));
        fprintf('--------------------------------------------------\n');
    else
        fprintf('\n错误: 无法从 C++ 程序输出中解析张量。\n');
        fprintf('程序输出:\n%s\n', cmdout);
        error('张量解析失败。');
    end

end